/**
 * 在生产环境 代理是无法生效的，所以这里没有生产环境的配置
 * The agent cannot take effect in the production environment
 * so there is no configuration of the production environment
 * For details, please see
 * https://pro.ant.design/docs/deploy
 */

//  国内：正式 https://api.petkit.com     测试 https://api-sandbox.petkit.com
//  海外：正式     测试

const localDomain = '192.168.17.45:9999';
// const localDomain = 'bs-local-devapi.petkit.com';
const mockRootDomain = 'yapi.petkit.com/mock';
const domains: { [key: string]: string } = {
  mockDomains: `${mockRootDomain}/${457}/`,
  sandboxDomains: 'api-sandbox.petkit.com/6',
  // sandboxDomains: 'api-sandbox.petktasia.com/6',
  // sandboxDomains: 'api-sandbox.eu-pet.com/6',
  // onlineDomains: 'api.eu-pet.com/latest',
  onlineDomains: 'api.petktasia.com/latest',
};

const getProxyApiInfo = (domain: string) => {
  return {
    '/bs': {
      target: domain,
      changeOrigin: true,
      pathRewrite: { '^': '' },
      secure: true,
    },
  };
};

const proxy: { [key: string]: { [key: string]: any } } = {
  local: {
    ...getProxyApiInfo(`http://${localDomain}`),
  },
  mock: {
    ...getProxyApiInfo(`http://${domains.mockDomains}`),
  },
  sandbox: {
    ...getProxyApiInfo(`https://${domains.sandboxDomains}`),
  },
  online: {
    ...getProxyApiInfo(`https://${domains.onlineDomains}`),
  },
};

export default proxy;
