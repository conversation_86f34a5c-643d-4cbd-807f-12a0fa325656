import { serviceTimeUnitNames } from '@/pages/Package/Sku/util';
import cloneDeep from 'lodash/cloneDeep';
import orderBy from 'lodash/orderBy';
import {
  BenefitDetail,
  Currency,
  LevelEnum,
  LevelSkuGroup,
  ProductSku,
  ServiceTimeUnitEnum,
  SuitableDeviceTypeEnum,
} from './interface';

// const localeKeyMap = localeConfiguration[getLocaleKey()];

export const initCurrency: Currency = {
  countryCode: '',
  countryName: '',
  currencySymbol: '',
  currencyCode: '',
  id: 0,
};

export const initProductSku: ProductSku = {
  id: 0,
  name: '',
  shortName: '',
  aliasName: '',
  saleStatus: true,
  level: 0,
  /**
   * @deprecated capacities 已废弃
   */
  capacities: [],
  /**
   * @deprecated cycleTime 已废弃
   */
  cycleTime: 0,
  serviceTime: 1,
  serviceTimeUnit: ServiceTimeUnitEnum.DAY,
  deviceTypes: [],
  price: {
    price: 0,
    linePrice: 0,
    firstPhasePrice: 0,
    isReNew: false,
    currency: initCurrency,
  },
  cornerMarkIcon: '',
  description: '',
  relationSkuId: 0,
  benefits: [],
  // actPackage?: ProductSkuActivityInfo;
  createTime: 0,
};

export const initBenefitTableData: string[][] = [
  ['', '免费'],
  ['价格', '0'],
  ['事件图像', '1天循环'],
  ['动态视频回看', '/'],
  ['1080P 完整视频回看', '/'],
];

const benefitNameIndexMap: { [key: number]: keyof BenefitDetail } = {
  2: 'image',
  3: 'highlight',
  4: 'playback1080p',
  // 非固定
  5: 'vlog',
  6: 'displayTheme',
  7: 'fecesPic',
  8: 'healthyAnalysis',
};

export const levelBenefitMap: { [key in LevelEnum]: BenefitDetail } = {
  [LevelEnum.MINIMUE]: {
    image: '/',
    highlight: '/',
    playback1080p: '/',
    vlog: '/',
    displayTheme: '/',
    fecesPic: '/',
    healthyAnalysis: '/',
  },
  [LevelEnum.BASIC_MINUS]: {
    image: '/',
    highlight: '/',
    playback1080p: '/',
    vlog: '/',
    displayTheme: '/',
    fecesPic: '/',
    healthyAnalysis: '/',
  },
  [LevelEnum.BASIC]: {
    image: '7天循环',
    highlight: '7天循环',
    playback1080p: '1天循环',
    vlog: 'HD',
    displayTheme: '支持',
    fecesPic: '7天循环',
    healthyAnalysis: '支持',
  },
  [LevelEnum.PREMIUM]: {
    image: '7天循环',
    highlight: '7天循环',
    playback1080p: '7天循环',
    vlog: 'HD',
    displayTheme: '支持',
    fecesPic: '7天循环',
    healthyAnalysis: '支持',
  },
  [LevelEnum.PREMIUM_PLUS]: {
    image: '30天循环',
    highlight: '30天循环',
    playback1080p: '30天循环',
    vlog: 'HD',
    displayTheme: '支持',
    fecesPic: '30天循环',
    healthyAnalysis: '支持',
  },
};

export const initLevelSkuGroup: LevelSkuGroup = {
  [LevelEnum.MINIMUE]: {
    [ServiceTimeUnitEnum.DAY]: [],
    [ServiceTimeUnitEnum.MONTH]: [],
    [ServiceTimeUnitEnum.YEAR]: [],
  },
  [LevelEnum.BASIC_MINUS]: {
    [ServiceTimeUnitEnum.DAY]: [],
    [ServiceTimeUnitEnum.MONTH]: [],
    [ServiceTimeUnitEnum.YEAR]: [],
  },
  [LevelEnum.BASIC]: {
    [ServiceTimeUnitEnum.DAY]: [],
    [ServiceTimeUnitEnum.MONTH]: [],
    [ServiceTimeUnitEnum.YEAR]: [],
  },
  [LevelEnum.PREMIUM]: {
    [ServiceTimeUnitEnum.DAY]: [],
    [ServiceTimeUnitEnum.MONTH]: [],
    [ServiceTimeUnitEnum.YEAR]: [],
  },
  [LevelEnum.PREMIUM_PLUS]: {
    [ServiceTimeUnitEnum.DAY]: [],
    [ServiceTimeUnitEnum.MONTH]: [],
    [ServiceTimeUnitEnum.YEAR]: [],
  },
};

export const getLevelSkuGroup = (productSkuList: ProductSku[]) => {
  const levelSkuGroup = cloneDeep(initLevelSkuGroup);

  productSkuList.forEach((sku) => {
    const { actPackage, level, serviceTimeUnit } = sku;
    if (
      actPackage ||
      serviceTimeUnit === ServiceTimeUnitEnum.DAY ||
      !sku.price.isReNew ||
      level > LevelEnum.MINIMUE
    ) {
      return;
    }
    levelSkuGroup[level][serviceTimeUnit].push(sku);
  });

  // console.log(levelSkuGroup);
  return levelSkuGroup;
};

export const getBenefitTableData = (
  levelSkuGroup: LevelSkuGroup,
  allSkuList: ProductSku[],
  deviceType: SuitableDeviceTypeEnum,
) => {
  const benefitTableData = cloneDeep(initBenefitTableData);
  const localeCurrency =
    allSkuList.filter((sku) => !!sku.price.currency)[0]?.price.currency
      ?.currencySymbol || '$';
  benefitTableData[1][1] = `${localeCurrency}${benefitTableData[1][1]}`;

  // // 调整动态视频权益：D4sh&D4h免费权益中没有该功能
  // if (
  //   deviceType === SuitableDeviceTypeEnum.D4sh ||
  //   deviceType === SuitableDeviceTypeEnum.D4h
  // ) {
  //   benefitTableData[3][1] = '/';
  // }

  // // for (let i = 0; i <= LevelEnum.MINIMUE; i++) {
  // // 1080P高清特权: D4sh D4h
  // const playback1080pBenefitData = [
  //   'package.selector.benefit.table.1080P',
  //   '/',
  // ];
  // const playback1080pDeviceTypes: string[] = [
  //   SuitableDeviceTypeEnum.D4h.toLocaleLowerCase(),
  //   SuitableDeviceTypeEnum.D4sh.toLocaleLowerCase(),
  // ];

  // // 完整视频回放: T5 T6 T7
  // const fullPlaybackBenefitData = [
  //   'package.selector.benefit.table.fullPlayback',
  //   'package.selector.benefit.table.fullPlayback.free',
  // ];
  // const fullPlaybackDeviceTypes: string[] = [
  //   SuitableDeviceTypeEnum.T5.toLocaleLowerCase(),
  //   SuitableDeviceTypeEnum.T6.toLocaleLowerCase(),
  //   SuitableDeviceTypeEnum.T7.toLocaleLowerCase(),
  // ];

  // 每日精彩特权:D4sh D4h T6
  const vlogBenefitData = ['每日精彩', 'SD'];
  const vlogDeviceTypes: string[] = [
    SuitableDeviceTypeEnum.D4h.toLocaleLowerCase(),
    SuitableDeviceTypeEnum.D4sh.toLocaleLowerCase(),
    SuitableDeviceTypeEnum.T6.toLocaleLowerCase(),
  ];
  // 专属装扮特权:D4sh D4h
  const displayThemeBenefitData = ['专属装扮', '/'];
  const displayThemeDeviceTypes: string[] = [
    SuitableDeviceTypeEnum.D4h.toLocaleLowerCase(),
    SuitableDeviceTypeEnum.D4sh.toLocaleLowerCase(),
  ];
  // 便便图特权: T5 T6
  const fecesPicBenefitData = ['便便图', '1天循环'];
  const fecesPicDeviceTypes = [
    SuitableDeviceTypeEnum.T5.toLocaleLowerCase(),
    SuitableDeviceTypeEnum.T6.toLocaleLowerCase(),
  ];

  // 健康分析: T7
  const healthyAnalysisData = [
    'package.selector.benefit.table.healthyAnalysis',
    '/',
  ];
  const healthyAnalysisDataTypes = [
    SuitableDeviceTypeEnum.T7.toLocaleLowerCase(),
  ];

  for (let i = LevelEnum.MINIMUE; i >= LevelEnum.PREMIUM_PLUS; i--) {
    const skuGroup = levelSkuGroup[i as LevelEnum];
    const monthSkuList = orderBy(
      skuGroup[ServiceTimeUnitEnum.MONTH] || [],
      ['createTime'],
      ['desc'],
    );
    const yearSkuList = orderBy(
      skuGroup[ServiceTimeUnitEnum.YEAR] || [],
      ['createTime'],
      ['desc'],
    );
    if (!monthSkuList.length && !yearSkuList.length) continue;
    const skuList: ProductSku[] = monthSkuList
      .slice(0, 1)
      .concat(yearSkuList.slice(0, 1));

    for (let j = 0; j < benefitTableData.length; j++) {
      if (j === 0) {
        // benefitTableData[j][i + 2 + j] = skuList[0].shortName;
        benefitTableData[j].push(skuList[0].shortName);
      } else if (j === 1) {
        // benefitTableData[j][i + 2 + j] = skuList
        benefitTableData[j].push(
          skuList
            .map(
              (skuInfo) =>
                `${skuInfo.price.currency?.currencySymbol || '$'}${
                  skuInfo.price.price
                }/${serviceTimeUnitNames[skuInfo.serviceTimeUnit]}`,
            )
            .join('\n'),
        );
      } else {
        const benefitDetailKey: keyof BenefitDetail =
          benefitNameIndexMap[j as number];
        // benefitTableData[j][i + 2 + j] =
        //   levelBenefitMap[i as LevelEnum][benefitDetailKey];
        benefitTableData[j].push(
          levelBenefitMap[i as LevelEnum][benefitDetailKey] || '/',
        );
      }
    }

    // // 1080P高清特权: D4sh D4h
    // playback1080pBenefitData.push(
    //   levelBenefitMap[i as LevelEnum][benefitNameIndexMap[4]] || '/',
    // );

    // // 完整视频回放: T5 T6 T7
    // fullPlaybackBenefitData.push(
    //   levelBenefitMap[i as LevelEnum][benefitNameIndexMap[5]] || '/',
    // );

    // 每日精彩特权:D4sh D4h T6
    vlogBenefitData.push(
      levelBenefitMap[i as LevelEnum][benefitNameIndexMap[5]] || '/',
    );
    // 专属装扮特权:D4sh D4h
    displayThemeBenefitData.push(
      levelBenefitMap[i as LevelEnum][benefitNameIndexMap[6]] || '/',
    );
    // 便便图特权: T5 T6
    fecesPicBenefitData.push(
      levelBenefitMap[i as LevelEnum][benefitNameIndexMap[7]] || '/',
    );
    // 健康分析：T7
    // healthyAnalysisData.push(
    //   levelBenefitMap[i as LevelEnum][benefitNameIndexMap[9]] || '/',
    // );
  }

  if (vlogDeviceTypes.includes(deviceType.toLocaleLowerCase())) {
    benefitTableData.push(vlogBenefitData);
  }

  if (displayThemeDeviceTypes.includes(deviceType.toLocaleLowerCase())) {
    benefitTableData.push(displayThemeBenefitData);
  }

  if (fecesPicDeviceTypes.includes(deviceType.toLocaleLowerCase())) {
    benefitTableData.push(fecesPicBenefitData);
  }

  // if (healthyAnalysisDataTypes.includes(deviceType.toLocaleLowerCase())) {
  //   benefitTableData.push(healthyAnalysisData);
  // }

  return benefitTableData;
};
