import { Effect, Reducer } from '@umijs/max';
import {
  fetchBenefitListByDevice,
  fetchBundleSkuList,
  fetchDefaultBenefitList,
  fetchMinPriceBundleSku,
  fetchSuitableProductList,
} from './fetch';
import {
  BundleSku,
  CloudStorageBenefit,
  SuitableProductListParam,
  type Benefit,
  type ProductSku,
} from './interface';
import { getBenefitTableData, getLevelSkuGroup } from './util';

export interface ProductState {
  productSkuList: ProductSku[];
  selectedProductSku?: ProductSku;
  benefitList: Benefit[];
  benefitTableData: string[][];
  defaultBenefitList: CloudStorageBenefit[];
  bundleSkuList: BundleSku[];
  minPriceBundleSku?: BundleSku;
}

export const initProductState: ProductState = {
  productSkuList: [],
  selectedProductSku: undefined,
  benefitList: [],
  benefitTableData: [],
  defaultBenefitList: [],
  bundleSkuList: [],
  minPriceBundleSku: undefined,
};

export interface ProductModel {
  namespace: 'product';
  state: ProductState;
  effects: {
    requestSuitableProductList: Effect;
    requestBenefitList: Effect;
    requestDefaultBenefitList: Effect;
    requestBundleSkuList: Effect;
    requestMinPriceBundleSku: Effect;
  };
  reducers: {
    requestSuitableProductListSuccess: Reducer<
      ProductState,
      {
        type: 'requestSuitableProductListSuccess';
        payload: {
          productList: ProductSku[];
          requestParam: SuitableProductListParam;
        };
      }
    >;
    selectProductSkuById: Reducer<
      ProductState,
      { type: 'selectProductSkuById'; payload: number }
    >;
    requestBenefitListSuccess: Reducer<
      ProductState,
      { type: 'requestBenefitListSuccess'; payload: Benefit[] }
    >;
    requestDefaultBenefitListSuccess: Reducer<
      ProductState,
      {
        type: 'requestDefaultBenefitListSuccess';
        payload: CloudStorageBenefit[];
      }
    >;
    requestBundleSkuListSuccess: Reducer<
      ProductState,
      { type: 'requestBundleSkuListSuccess'; payload: BundleSku[] }
    >;
    requestMinPriceBundleSkuSuccess: Reducer<
      ProductState,
      { type: 'requestMinPriceBundleSkuSuccess'; payload: BundleSku }
    >;
  };
}

const productModel: ProductModel = {
  namespace: 'product',
  state: initProductState,
  effects: {
    *requestSuitableProductList({ payload }, { call, put }) {
      const productList: ProductSku[] = yield call(
        fetchSuitableProductList,
        payload,
      );
      yield put({
        type: 'requestSuitableProductListSuccess',
        payload: { productList, requestParam: payload },
      });
    },
    *requestBenefitList({ payload }, { call, put }) {
      const benefitList: Benefit[] = yield call(
        fetchBenefitListByDevice,
        payload,
      );
      yield put({
        type: 'requestBenefitListSuccess',
        payload: benefitList && benefitList.length ? benefitList : [],
      });
    },
    *requestDefaultBenefitList(_, { call, put }) {
      const list: CloudStorageBenefit[] = yield call(fetchDefaultBenefitList);
      yield put({
        type: 'requestDefaultBenefitListSuccess',
        payload: list,
      });
    },
    *requestBundleSkuList({ payload }, { call, put }) {
      const bundleSkuList: BundleSku[] = yield call(
        fetchBundleSkuList,
        payload,
      );
      yield put({
        type: 'requestBundleSkuListSuccess',
        payload: bundleSkuList || [],
      });
    },
    *requestMinPriceBundleSku({ payload }, { call, put }) {
      const result: BundleSku = yield call(fetchMinPriceBundleSku, payload);
      yield put({
        type: 'requestMinPriceBundleSkuSuccess',
        payload: result,
      });
    },
  },
  reducers: {
    requestSuitableProductListSuccess(
      state = initProductState,
      { payload },
    ): ProductState {
      const { productList, requestParam } = payload;
      // console.log('requestSuitableProductListSuccess', payload);
      const levelSkuGroup = getLevelSkuGroup(productList);

      const benfitTableData = getBenefitTableData(
        levelSkuGroup,
        productList,
        requestParam.deviceType,
      );

      return {
        ...state,
        productSkuList: productList,
        benefitTableData: [...benfitTableData],
      };
    },
    selectProductSkuById(state = initProductState, { payload }): ProductState {
      const { productSkuList } = state;
      const productSku = productSkuList.find((item) => item.id === payload);
      return {
        ...state,
        selectedProductSku: productSku,
      };
    },
    requestBenefitListSuccess(
      state = initProductState,
      { payload },
    ): ProductState {
      return {
        ...state,
        benefitList: payload,
      };
    },
    requestDefaultBenefitListSuccess(
      state = initProductState,
      { payload },
    ): ProductState {
      return {
        ...state,
        defaultBenefitList: payload,
      };
    },
    requestBundleSkuListSuccess(
      state = initProductState,
      { payload },
    ): ProductState {
      return {
        ...state,
        bundleSkuList: payload,
      };
    },
    requestMinPriceBundleSkuSuccess(
      state = initProductState,
      { payload },
    ): ProductState {
      return {
        ...state,
        minPriceBundleSku: payload,
      };
    },
  },
};

export default productModel;
