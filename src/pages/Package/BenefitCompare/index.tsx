import handpickIcon from '@/assets/nike.svg';
import { fetchDefaultBenefitList } from '@/models/product/fetch';
import { ProductSku } from '@/models/product/interface';
import { Image } from 'antd-mobile';
import { DownOutline } from 'antd-mobile-icons';
import React, { useEffect, useMemo, useState } from 'react';
import { BenefitCompareData, FeatureValue } from './interface';
import { mockBenefitCompareData } from './mockData';
import {
  processYearlySkuList,
  transformCloudStorageToBenefitCompare,
  transformSkuListToBenefitCompare,
} from './utils';

interface BenefitCompareTableProps {
  data?: BenefitCompareData;
  yearlyProductSkuList: ProductSku[];
}

const BenefitCompareTable: React.FC<BenefitCompareTableProps> = ({
  data,
  yearlyProductSkuList = [],
}) => {
  const [benefitData, setBenefitData] = useState<BenefitCompareData>(
    mockBenefitCompareData,
  );
  const [loading, setLoading] = useState<boolean>(false);

  // 使用 useMemo 来稳定化 yearlyProductSkuList 的依赖
  const skuListLength = yearlyProductSkuList.length;
  const skuListIds = useMemo(
    () => yearlyProductSkuList.map((sku) => sku.id).join(','),
    [yearlyProductSkuList],
  );

  useEffect(() => {
    const fetchBenefitData = async () => {
      try {
        setLoading(true);

        // 优先使用传入的SKU数据
        if (yearlyProductSkuList && yearlyProductSkuList.length > 0) {
          const processedSkuList = processYearlySkuList(yearlyProductSkuList);
          const skuBasedData =
            transformSkuListToBenefitCompare(processedSkuList);
          setBenefitData(skuBasedData);
          return;
        }

        // 如果没有SKU数据，则从接口获取默认权益数据
        const response = await fetchDefaultBenefitList();

        if (response.items && response.items.length > 0) {
          const transformedData = transformCloudStorageToBenefitCompare(
            response.items,
          );
          setBenefitData(transformedData);
        } else {
          setBenefitData(mockBenefitCompareData);
        }
      } catch (err) {
        console.error('获取权益数据失败:', err);
        setBenefitData(mockBenefitCompareData);
      } finally {
        setLoading(false);
      }
    };

    fetchBenefitData();
  }, [skuListLength, skuListIds]);

  // 使用传入的 data 或内部状态的 benefitData
  const displayData = data || benefitData;

  // 动态计算grid列模板
  const planCount = displayData.plans.length;

  const gridTemplateColumns = `120px ${'80px '.repeat(planCount).trim()}`;

  const minTableWidth = `${120 + planCount * 80}px`;
  const gridCelStyle = {
    gridTemplateColumns,
    minWidth: minTableWidth,
  };

  // 渲染功能值的通用方法
  const renderFeatureValue = (value: FeatureValue): React.ReactNode => {
    if (typeof value === 'boolean') {
      return value ? (
        <Image
          src={handpickIcon}
          className="w-3.5 h-3.5 mx-auto md:w-3 md:h-3"
          alt="支持"
        />
      ) : null;
    }
    return (
      <span className="text-0.5xl font-bold active-color md:text-[11px]">
        {value}
      </span>
    );
  };

  if (loading) {
    return (
      <div className="">
        <h2 className="text-center font-bold text-base mb-4 active-color">
          Compare PETKIT Care+ Plans
        </h2>
        <div className="flex justify-center items-center py-8">
          <div className="text-gray-500">数据加载中...</div>
        </div>
      </div>
    );
  }

  return (
    <div className="">
      <h2 className="text-center font-bold text-3xl mb-4 active-color">
        权益对比
      </h2>

      <div className="overflow-hidden">
        <div className="overflow-x-auto">
          {/* 表头 - 套餐信息 */}
          <div className="grid gap-0 table-bg-color" style={gridCelStyle}>
            {/* 空白列，对应功能名称列 */}
            <div className="p-1.5 text-center bg-transparent font-semibold md:p-3 border-r border-r-[#f0f0f0]">
              {/* 空白 */}
            </div>

            {/* 套餐列 */}
            {displayData.plans.map((plan, index) => (
              <div
                key={plan.id}
                className={`p-1.5 text-center md:p-3 border-r border-r-[#f0f0f0] last-of-type:border-none overflow-hidden`}
              >
                <div className="active-color font-bold text-xs mb-1 truncate">
                  {plan.name}
                </div>
                <div className="active-color font-bold text-xs mb-0.5 truncate">
                  {plan.currency}
                  {plan.price}
                </div>
                {index !== 0 ? (
                  <div className="secondary-text-color text-[7px] opacity-80">
                    {plan.billing}
                  </div>
                ) : null}
              </div>
            ))}
          </div>

          {/* 表体 - 功能对比 */}
          <div className="w-full" style={gridCelStyle}>
            {displayData.features.map((feature, featureIndex) => (
              <div
                key={feature.id}
                className={`${
                  featureIndex % 2 === 0 ? 'bg-white' : 'table-bg-color'
                }`}
              >
                <div className="grid gap-0" style={gridCelStyle}>
                  {/* 功能名称列 */}
                  <div className="p-1.5 text-left secondary-text-color text-xs font-medium flex items-start md:p-3 md:text-[11px]  border-r border-r-[#f0f0f0] last-of-type:border-none ">
                    <DownOutline
                      className="primary-text-color mr-2 font-bold mt-0.5 flex-shrink-0"
                      fontSize={12}
                    />
                    <div className="flex-1">
                      <div className="primary-text-color text-xs">
                        {feature.name}
                      </div>
                      {feature.description && (
                        <div className="secondary-text-color text-[10px] mt-1 opacity-70 leading-tight md:text-[9px]">
                          {feature.description}
                        </div>
                      )}
                    </div>
                  </div>

                  {/* 各套餐对应的功能值 */}
                  {displayData.plans.map((plan) => (
                    <div
                      key={`${feature.id}-${plan.id}`}
                      className="p-1.5 text-center flex items-center justify-center md:p-3  border-r border-r-[#f0f0f0] last-of-type:border-none "
                    >
                      {renderFeatureValue(plan.features[feature.id])}
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default BenefitCompareTable;
