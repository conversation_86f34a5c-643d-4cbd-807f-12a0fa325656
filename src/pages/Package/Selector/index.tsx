import Protocol from '@/components/Protocol';
import {
  DeviceInfoWithPackage,
  DeviceInfoWithPackageParam,
} from '@/models/device/interface';
import {
  fetchOrderCreationV2,
  fetchOrderStateCheck,
  fetchPaymentResult,
  fetchSubscribeResult,
} from '@/models/order/fetch';
import {
  OrderCreationResult,
  OrderCreationV2Param,
  OrderStatusEnum,
  PayTypeEnum,
  SubscriptionResult,
  SubscriptionStateEnum,
} from '@/models/order/interface';
import {
  ActTimeEnum,
  CloudStorageBenefit,
  ProductSku,
  ServiceTimeUnitEnum,
  SuitableDeviceTypeEnum,
  SuitableProductListParam,
} from '@/models/product/interface';
import { DescriptionTypeEnum } from '@/pages/CloudService/interface';
import MultipleDevicePlan from '@/pages/Package/MultipleDevicePlan/Entrance';
import global, { ExtraNativeMessageParam } from '@/utils/global';
import useUrlState from '@ahooksjs/use-url-state';
import type { Dispatch } from '@umijs/max';
import { history, useDispatch, useSelector } from '@umijs/max';
import { Dialog, Image, Mask, Popup, Toast } from 'antd-mobile';
import React, { useEffect, useMemo, useRef, useState } from 'react';
import PlanCompareTable from '../BenefitCompare';
import Confirm from '../Confirm';
import BenefitHighLight from '../Sku/BenefitHighLight';
import { OrderDuration } from '../Sku/interface';
import SkuSelector from '../Sku/Selector';
import {
  serviceTimeUnitNames,
  transferProductSkuListToOrderDurationList,
} from '../Sku/util';
import DeviceInfo from './DeviceInfo';
import styles from './index.less';
// import { PointTypeEnum, AplusBuyMethodTypeEnum } from '@/utils/aplus';
// import { PaymentInfo } from '../Confirm/interface';
// import { fetchSubscribeResult } from '@/models/order/fetch';
import activityImage from '@/assets/activity-image.png';
import closeButton from '@/assets/close-button.png';
import PageContainer from '@/components/PageContainer';
import PriceInfo from '@/components/PriceInfo';
import { EnterEnum, NumberBooleanEnum } from '@/models/common.interface';
import { ConnectState } from '@/models/connect';
import { AplusPayResultTypeEnum, PointTypeEnum } from '@/utils/aplus';
import { Action } from 'antd-mobile/es/components/popover';
import classNames from 'classnames';
import dayjs from 'dayjs';
import { getValidDataMessage } from '../../util';
import { selectorPageService } from './service';

interface QueryParam {
  deviceId: string;
  deviceType: SuitableDeviceTypeEnum;
  orderId?: string;
  subNo?: string;
  state?: OrderStatusEnum;
}

const initQueryParam: QueryParam = {
  deviceId: '',
  deviceType: SuitableDeviceTypeEnum.D4sh,
};

let countForConsole = 0;

const PackageSelector: React.FC = () => {
  const [urlParam] = useUrlState<QueryParam>(initQueryParam);
  const dispatch: Dispatch = useDispatch();
  const [selectorTabList, setSelectorTabList] = useState<OrderDuration[]>([]);
  // 使用 ReturnType<typeof setInterval> 替代 NodeJS.Timer
  const [timer, setTimer] = useState<ReturnType<typeof setInterval>>();
  const [buttonLoading, setButtonLoading] = useState(false);
  const [showPayment, setShowPayment] = useState(false);
  const [showMask, setShowMask] = useState(false);
  const scrollRef = useRef<HTMLDivElement>(null);

  const productSkuList: ProductSku[] = useSelector(
    ({ product }: ConnectState) => product.productSkuList,
  );
  const selectedProductSku: ProductSku | undefined = useSelector(
    ({ product }: ConnectState) => product.selectedProductSku,
  );
  const deviceInfo: DeviceInfoWithPackage | undefined = useSelector(
    ({ device }: ConnectState) => device.deviceInfo,
  );
  const defaultBenefitList: CloudStorageBenefit[] = useSelector(
    ({ product }: ConnectState) => product.defaultBenefitList,
  );
  // const devicePackageInfo = useMemo(() => {
  //   return deviceInfo?.effectiveProductInfo;
  // }, [deviceInfo]);

  // const productSkuInfoList: ProductSku[] = useMemo(() => {
  //   if ((!productSkuInfoList || !productSkuInfoList.length)) return [];

  //   if (!defaultBenefitList || !defaultBenefitList.length) return productSkuList;

  // }, [productSkuList, defaultBenefitList])

  const goBack = () => {
    global.closeWebview();
  };

  const requestReceive = async () => {
    if (!deviceInfo || !urlParam) return;

    if (!selectedProductSku) {
      Toast.show({
        content: '当前没有可领取的免费套餐，请联系客服反馈',
      });
      return;
    }

    const param: OrderCreationV2Param = {
      deviceId: deviceInfo.deviceId,
      skuId: selectedProductSku.id,
      deviceType: urlParam.deviceType,
      platform: PayTypeEnum.EXPERIENCE,
      upgrade: NumberBooleanEnum.FALSE,
      activityId: selectedProductSku.actPackage?.actPackageId,
    };

    setTimeout(async () => {
      try {
        const payResult = await fetchOrderCreationV2(param);
        if (payResult && payResult.state === OrderStatusEnum.PAY_SUCCEED) {
          history.push('/receive/result');
        }
      } catch (err: any) {
        Toast.show({
          content: err.message || '领取失败',
          icon: 'fail',
        });
      } finally {
        setButtonLoading(false);
      }
    }, 500);
  };

  // 同步签约结果
  const requestAsyncSubscriptionResult = async (_subNod: string) => {
    const result = await fetchSubscribeResult({ subNo: _subNod });
    setTimeout(() => {
      setTimer(undefined);
      clearInterval(timer);
      if (result.state === SubscriptionStateEnum.ACTIVITY) {
        // 跳转到签约成功页面
        history.push(
          `/subscription/result?deviceId=${urlParam.deviceId}&deviceType=${
            urlParam.deviceType
          }&subNo=${urlParam.subNo || _subNod}&state=${result.state}`,
        );
      } else if (result.state === SubscriptionStateEnum.SIGNING) {
        // 跳转到提醒到支付宝完成签约的界面
        history.push(
          `/subscription/result?deviceId=${urlParam.deviceId}&deviceType=${
            urlParam.deviceType
          }&subNo=${urlParam.subNo || _subNod}&state=${result.state}`,
        );
      } else {
        let statusName = '签约已解约~';
        if (result.state === SubscriptionStateEnum.CANCELLED)
          statusName = '签约已取消~';
        Dialog.clear();
        Toast.show(statusName);
      }
    }, 3000);
  };

  // 同步支付结果
  const requestAsyncPaymentResult = async (_orderId: string) => {
    const result = await fetchPaymentResult(_orderId);
    if (result.state === OrderStatusEnum.PAY_SUCCEED) {
      global.aplus.buryPoint(PointTypeEnum.BUYNOW_PAID_RESULT, {
        method: AplusPayResultTypeEnum.SUCCESS,
      });
      const toastRef = Toast.show({
        icon: 'loading',
        // content: '加载中…',
        duration: 0,
      });
      const _timer = setTimeout(() => {
        // 跳转到我的云服务
        global.gotoCurrentDeviceServicePage();
        clearTimeout(_timer);
        toastRef.close();
      }, 3000);
    } else if (result.state === OrderStatusEnum.PAYING) {
      history.replace(`/order/list`);
    }
    setTimer(undefined);
    clearInterval(timer);
  };

  // 判断当前所选套餐是否存在未支付的情况
  const requestOrderStateCheck = async () => {
    const result = await fetchOrderStateCheck();
    return result;
  };

  // 确认支付
  const goToPayment = async () => {
    // 统计点击多少次
    global.aplus.buryPoint(PointTypeEnum.BUYNOW, {
      Enter: sessionStorage.getItem('entrance') || EnterEnum.UNKNOWN,
    });
    setButtonLoading(true);
    if (!window.navigator.onLine) {
      Toast.show('网络连接断开，请检查网络设置~');
      setButtonLoading(false);
      return;
    }

    if (!selectedProductSku) {
      Toast.show('当前没有可购买的套餐，请先选择套餐');
      setButtonLoading(false);
      return;
    }

    // if (devicePackageInfo && devicePackageInfo.subscribe) {
    //   Toast.show('该设备处于订阅中，无法重复购买');
    //   setButtonLoading(false);
    //   return;
    // }

    if (!deviceInfo || !deviceInfo.deviceId) {
      Toast.show(`查无此设备，请检查是否选择了${urlParam.deviceType}设备~`);
      setButtonLoading(false);
      return;
    }

    const orderStateCheckResult = await requestOrderStateCheck();
    // const orderStateCheckResult = null;
    if (
      orderStateCheckResult &&
      orderStateCheckResult[OrderStatusEnum.PAYING] &&
      orderStateCheckResult[OrderStatusEnum.PAYING].length
    ) {
      Dialog.confirm({
        content: '您有待支付的订单，请先处理',
        confirmText: (
          <div style={{ color: '#3378F8', fontSize: 15, fontWeight: 400 }}>
            查看订单
          </div>
        ),
        cancelText: <div style={{ color: '#999', fontSize: 15 }}>取消</div>,
        onConfirm: () => {
          history.push(
            `/order/list?deviceId=${urlParam.deviceId}&deviceType=${urlParam.deviceType}`,
          );
        },
      });
      setButtonLoading(false);
      return;
    }

    if (
      selectedProductSku.price.price === 0 ||
      (selectedProductSku.actPackage &&
        selectedProductSku.actPackage.price &&
        selectedProductSku.actPackage.price.amount === 0)
    ) {
      requestReceive();
      return;
    }

    if (
      !deviceInfo.effectiveProductInfo &&
      !(deviceInfo.pendingProductInfos || []).length
    ) {
      // 正常购买
      setButtonLoading(false);
      setShowPayment(true);
      return;
    }

    const validDataMessage = getValidDataMessage(
      deviceInfo,
      selectedProductSku,
    );

    if (validDataMessage) {
      Dialog.alert({
        title: '温馨提示',
        content: validDataMessage,
      });
      setButtonLoading(false);
      return;
    }

    history.push('/mention');
  };
  // 展示是否完成支付的弹框
  const showPaymentResultDialog = (orderId: string) => {
    Dialog.clear();
    Dialog.confirm({
      // title: '确认',
      content: (
        <div
          style={{ textAlign: 'center' }}
          onClick={() => {
            countForConsole += 1;
            console.log(countForConsole);
            if (countForConsole >= 3) {
              global.setupVConsole();
            }
          }}
        >
          您是否已完成支付？
        </div>
      ),
      confirmText: (
        <span style={{ fontSize: 14, fontWeight: 'normal' }}>已完成支付</span>
      ),
      cancelText: (
        <span style={{ fontSize: 14, fontWeight: 'bold', color: '#999' }}>
          支付碰到问题
        </span>
      ),
      onCancel: () => {
        sessionStorage.removeItem('payResultInfo');
        requestAsyncPaymentResult(orderId);
      },
      onConfirm: () => {
        sessionStorage.removeItem('payResultInfo');
        requestAsyncPaymentResult(orderId);
      },
    });
  };

  // 展示是否完成签约的弹框
  const showSubscriptionResultDialog = (subNod: string) => {
    Dialog.clear();
    Dialog.confirm({
      // title: '确认',
      content: (
        <div
          style={{ textAlign: 'center' }}
          onClick={() => {
            countForConsole += 1;
            console.log(countForConsole);
            if (countForConsole >= 3) {
              global.setupVConsole();
            }
          }}
        >
          您是否已完成签约？
        </div>
      ),
      confirmText: (
        <span style={{ fontSize: 14, fontWeight: 'normal' }}>已完成</span>
      ),
      cancelText: (
        <span style={{ fontSize: 14, fontWeight: 'bold', color: '#999' }}>
          签约碰到问题
        </span>
      ),
      onCancel: () => {
        sessionStorage.removeItem('subscribeResultInfo');
        requestAsyncSubscriptionResult(subNod);
      },
      onConfirm: () => {
        sessionStorage.removeItem('subscribeResultInfo');
        requestAsyncSubscriptionResult(subNod);
      },
    });
  };

  // 判断页面是是否存在待确认的支付订单
  const confirmOrderResult = () => {
    const orderCreationResultStr = sessionStorage.getItem('payResultInfo');
    sessionStorage.removeItem('payResultInfo');
    if (!orderCreationResultStr) return;
    const orderCreationResult: OrderCreationResult = JSON.parse(
      orderCreationResultStr,
    );
    if (orderCreationResult) {
      showPaymentResultDialog(orderCreationResult.orderId);
    }
  };

  // 判断页面是是否存在待确认的签约订单
  const confirmSubscriptionResult = () => {
    const resultStr = sessionStorage.getItem('subscribeResultInfo');
    sessionStorage.removeItem('subscribeResultInfo');
    if (!resultStr) return;
    const result: SubscriptionResult = JSON.parse(resultStr);
    if (result) {
      showSubscriptionResultDialog(result.subNo);
    }
  };

  // 关闭支付弹框
  const closeConfirm = () => {
    setShowPayment(false);
  };

  const gotoDescriptionPage = (type: DescriptionTypeEnum) => {
    // if (type === DescriptionTypeEnum.INTRODUCTION) {
    //   history.push('/entrance?hasNavBar=1');
    // } else {
    // }

    selectorPageService.pageScrollDistance = scrollRef.current?.scrollTop || 0;
    history.push(`/description/${type}?hasNavBar=1`);
  };

  const closeMask = () => {
    setShowMask(false);
  };

  const purchaseButtonText = useMemo(() => {
    if (!selectedProductSku) return <></>;
    // 活动：上部：{活动价格 === 0 ? 试用 : 开通} {服务时长} {SKU简称}
    // 活动：活动时间：开始日期-结束日期
    // 活动：底部：底部协议文案只有一个《PETKIT Care+ 服务说明协议》
    if (selectedProductSku.actPackage) {
      const {
        actPackage: {
          price: { amount, currency },
          serviceTime,
          serviceTimeUnit,
          effectTime,
          inEffectTime,
          actTimeEnum,
          price,
        },
        // shortName,
      } = selectedProductSku;
      return (
        <>
          <span
            className={classNames(styles.buttonText, styles.buttonTextActivity)}
          >
            <PriceInfo
              price={amount}
              symbol={(currency || price)?.currencySymbol}
            />
            试用
            {serviceTime}
            {serviceTimeUnit === ServiceTimeUnitEnum.MONTH ? '个' : ''}
            {serviceTimeUnitNames[serviceTimeUnit as ServiceTimeUnitEnum]}
          </span>
          {actTimeEnum === ActTimeEnum.CUSTOM ? (
            <span
              className={classNames(styles.buttonTip, styles.buttonTipActivity)}
            >
              活动时间：{dayjs(effectTime).format('YYYY-MM-DD')} -{' '}
              {dayjs(inEffectTime).format('YYYY-MM-DD')}
            </span>
          ) : null}
        </>
      );
    }
    // 非活动套餐 自动续费
    if (selectedProductSku.price.isReNew) {
      return (
        <>
          <span className={styles.buttonText}>立即开通</span>
          <span className={styles.buttonTip}>到期自动续费，可随时取消</span>
        </>
      );
    }
    // 非活动套餐 非自动续费
    return (
      <>
        <span className={styles.buttonText}>立即开通</span>
      </>
    );
  }, [selectedProductSku]);

  const purchaseButtonProtocol = useMemo(() => {
    if (!selectedProductSku) return <></>;
    const desciptionProtocol = (
      <span
        onClick={() =>
          gotoDescriptionPage(DescriptionTypeEnum.DESCRIPTION_PROTOCOL)
        }
      >
        《PETKIT Care+ 服务说明协议》
      </span>
    );
    const autoRenewProtocol = (
      <span
        onClick={() =>
          gotoDescriptionPage(DescriptionTypeEnum.AUTO_RENEW_PROTOCOL)
        }
      >
        《自动续费服务协议》
      </span>
    );

    // 活动套餐，目前只可能是非自动续费套餐
    if (selectedProductSku.actPackage) {
      return <>{desciptionProtocol}</>;
    }
    // 非活动套餐 自动续费
    if (selectedProductSku.price.isReNew) {
      return (
        <>
          {desciptionProtocol}及{autoRenewProtocol}
        </>
      );
    }
    // 非活动套餐 非自动续费
    return <>{desciptionProtocol}</>;
  }, [selectedProductSku]);

  // const getTrailRestDays = (workIndate: number) => {
  //   const expiration = dayjs(workIndate).endOf('d');
  //   const now = dayjs().endOf('d').valueOf();
  //   const duration = expiration.valueOf() - now;
  //   const days = duration > 0 ? dayjs.duration(duration).asDays() : 0;
  //   return days;
  // };

  useEffect(() => {
    // skuService.openActivityMask(() => setShowMask(true));
    global.aplus.sendPV();
    global.aplus.buryPoint(PointTypeEnum.VISIT, {
      Enter: sessionStorage.getItem('entrance') || EnterEnum.UNKNOWN,
      devicetype: urlParam.deviceType || '未知设备',
    });
    // 页面load时，需要开启计时功能，以便记录当前页面停留时间
    const startNow = Date.now();
    console.log('selector useEffect');

    return () => {
      // 离开页面时，需要上报停留时间
      const endNow = Date.now();
      const times = Math.abs(startNow - endNow);
      global.aplus.buryPoint(PointTypeEnum.PAGESTAY, {
        times,
        Enter: sessionStorage.getItem('entrance') || EnterEnum.UNKNOWN,
      });
    };
  }, []);

  useEffect(() => {
    if (!productSkuList || !productSkuList.length) return;
    // 当前存在已选择的ProductSku时，需要将页面定位到记录到的高度上
    scrollRef.current?.scrollTo({
      top: selectorPageService.pageScrollDistance,
    });
    // selectorPageService.pageScrollDistance = 0;
  }, [scrollRef, productSkuList]);

  useEffect(() => {
    if (!urlParam) return;
    const deviceInfoParam: DeviceInfoWithPackageParam = {
      deviceId: +urlParam.deviceId,
      deviceType: urlParam.deviceType as SuitableDeviceTypeEnum,
    };
    // console.log('before 获取当前设备的信息');
    // 获取当前设备的信息
    dispatch({
      type: 'device/requestDeviceInfoWithPackage',
      payload: deviceInfoParam,
    });
    dispatch({
      type: 'product/requestSuitableProductList',
      payload: {
        deviceType: urlParam.deviceType,
        deviceId: urlParam.deviceId,
      } as SuitableProductListParam,
    });
  }, [dispatch]);

  useEffect(() => {
    console.log(productSkuList);
    setSelectorTabList(
      transferProductSkuListToOrderDurationList(productSkuList),
    );
  }, [productSkuList]);

  useEffect(() => {
    // Toast.show({
    //   icon: 'fail',
    //   content: '显示咯',
    //   duration: 100000000,
    // });
    if (!global.isInApp()) {
      // Dialog.confirm({
      //   title: '打开APP',
      //   content: '是否打开小佩宠物APP？',
      //   confirmText: '打开',
      //   cancelText: '取消',
      //   onConfirm: () => {
      //     window.location.href = 'https://file.petkit.cn';
      //   },
      //   onCancel: () => {},
      // });
      return;
    }

    let _timer: ReturnType<typeof setInterval> | undefined;
    const duration = 60 * 1000;
    if (urlParam.orderId && !urlParam.subNo) {
      // 如果state直接给到pay success,则直接跳转到已支付的成功页面
      if (urlParam.state === OrderStatusEnum.PAY_SUCCEED) {
        // 清除所有弹框；因为当支付宝支付成功后，回调会先进入首页，然后在进入我的云服务；
        // 此时首页的弹框并未去除；当从我的云服务界面进行手势返回的时候；首页和其弹框依然存在
        Dialog.clear();
        sessionStorage.removeItem('payResultInfo');
        // 跳转到我的云服务
        global.gotoCurrentDeviceServicePage();
        // global.closeWebview();
        return;
      }
      // 支付结果提醒
      showPaymentResultDialog(urlParam.orderId);
      _timer = setInterval(async () => {
        requestAsyncPaymentResult(urlParam.orderId);
      }, duration);
    } else if (urlParam.subNo && !urlParam.orderId) {
      // 签约结果提醒
      // 如果state直接给到pay success,则直接跳转到已签约的成功页面
      if (urlParam.state === OrderStatusEnum.PAY_SUCCEED) {
        // 跳转到签约成功页面
        history.push(
          `/subscription/result?subNo=${urlParam.subNo}&state=${urlParam.state}`,
        );
        return;
      }
      // 签约结果提醒
      showSubscriptionResultDialog(urlParam.subNo);
      _timer = setInterval(async () => {
        requestAsyncSubscriptionResult(urlParam.subNo);
      }, duration);
    } else {
      setTimeout(() => {
        // 检测是否有待确认支付结果的订单数据，但此功能仅限于在小佩的APP中才可以执行
        if (global.isInApp()) confirmOrderResult();
        if (global.isInApp()) confirmSubscriptionResult();
      }, 300);
    }

    setTimer(_timer);
  }, [urlParam]);

  return (
    <PageContainer
      enableDebug
      onBack={goBack}
      className="colorful-background"
      scrollRef={scrollRef}
      rightMenuActions={
        [
          {
            key: 'exchangeCenter',
            text: '激活中心',
            onClick: () => {
              if (!deviceInfo) {
                Toast.show('当前没有设备信息，请先确认设备信息再进行兑换操作!');
                return;
              }
              const extraParam: ExtraNativeMessageParam = {
                deviceId: deviceInfo.deviceId,
                deviceType: deviceInfo.deviceType,
                deviceName: deviceInfo.deviceName,
              };
              const devicePackageInfo =
                deviceInfo.effectiveProductInfo ||
                deviceInfo.expiredProductInfos?.[0];
              if (devicePackageInfo) {
                extraParam.packageName = devicePackageInfo.skuName;
                extraParam.packageExpirationTime = devicePackageInfo.workIndate;
                extraParam.packageRenewType = devicePackageInfo.subscribe;
              }
              global.gotoExchangeCenterPage(extraParam);
            },
          },
          {
            key: 'orderList',
            text: '我的订单',
            onClick: () => {
              history.push(
                `/order/list?deviceId=${urlParam.deviceId}&deviceType=${urlParam.deviceType}`,
              );
            },
          },
        ] as Action[]
      }
    >
      <div className="mb-[130px] p-4">
        <section className="mb-10">
          <DeviceInfo deviceInfo={deviceInfo} />
        </section>
        <section style={{ marginBottom: 36 }}>
          <SkuSelector orderDurationList={selectorTabList} />
        </section>

        <section className="mb-10">
          <MultipleDevicePlan />
        </section>

        <section className="-mx-4 mb-10">
          <BenefitHighLight />
        </section>

        {/* <SkuBenefit deviceType={urlParam.deviceType} /> */}
        <section className="mb-10">
          <PlanCompareTable
            yearlyProductSkuList={productSkuList.filter(
              (sku) => sku.serviceTimeUnit === ServiceTimeUnitEnum.YEAR,
            )}
          />
        </section>

        <Protocol onNavigate={gotoDescriptionPage} />
      </div>
      {selectedProductSku ? (
        <footer className={classNames(styles.footer, 'z-10')}>
          <section className="mb-7 flex items-center justify-center flex-col w-full">
            <button
              type="button"
              className={classNames('w-full', styles.purchaseButton, {
                [styles.purchaseButtonActivity]: selectedProductSku.actPackage,
              })}
              onClick={goToPayment}
              disabled={buttonLoading}
            >
              {purchaseButtonText}
            </button>
            <p
              className="gray-text-color mt-3.5"
              style={{
                marginLeft: 14,
                marginRight: 14,
                textAlign: 'center',
                fontSize: 12,
              }}
            >
              开通前阅读{purchaseButtonProtocol}
            </p>
          </section>
        </footer>
      ) : null}
      <Popup
        visible={showPayment}
        destroyOnClose
        bodyClassName={classNames('rounded-t-2xl', styles.popupBody)}
        showCloseButton
        bodyStyle={{
          height:
            !selectedProductSku?.price.isReNew &&
            selectedProductSku?.relationSkuId
              ? '370px'
              : '320px',
        }}
        onMaskClick={closeConfirm}
        onClose={closeConfirm}
      >
        <div className="p-5">
          <div className="flex justify-between items-center mb-8">
            {/* <div /> */}
            <h3 className="font-bold text-lg w-full text-center mb-0">
              订单结算
            </h3>
            {/* <CloseOutline
                className="text-lg font-bold"
                color="#111"
                onClick={closeConfirm}
              /> */}
          </div>
          {deviceInfo ? (
            <Confirm
              deviceId={deviceInfo.deviceId}
              deviceType={urlParam.deviceType}
              createSubscriptionSuccessFunction={(subNo: string) => {
                setShowPayment(false);
                showSubscriptionResultDialog(subNo);
              }}
              showRelativeSku={!selectedProductSku?.actPackage}
              createPaySuccessFunction={(
                orderId: string,
                payType: PayTypeEnum,
              ) => {
                if (payType === PayTypeEnum.WEIXIN) {
                  setShowPayment(false);
                  setTimeout(() => {
                    if (global.isiOS()) showPaymentResultDialog(orderId);
                  }, 800);
                }
              }}
            />
          ) : (
            ''
          )}
        </div>
      </Popup>

      <Mask visible={showMask}>
        <div className={styles.activityContainer}>
          <Image className={styles.activityContent} src={activityImage} />
          <Image
            src={closeButton}
            className={styles.activityClose}
            onClick={closeMask}
          />
          {/* <CloseCircleFill className={styles.activityClose} onClick={closeMask} /> */}
        </div>
      </Mask>
    </PageContainer>
  );
};

export default PackageSelector;
