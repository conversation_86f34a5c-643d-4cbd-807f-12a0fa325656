import type { ConnectState } from '@/models/connect';
import {
  ServiceTimeUnitEnum,
  type ProductSku,
} from '@/models/product/interface';
import type { Dispatch } from '@umijs/max';
import { useDispatch, useSelector } from '@umijs/max';
import { Image } from 'antd-mobile';
import classNames from 'classnames';
import React, { useEffect, useRef, useState } from 'react';
import SkuBenefitItem from '../Benefit/Item';
import { PackageSkuInfo } from '../interface';
import { serviceDurationNameObj } from '../util';
// import onSale from '@/assets/on-sale.png';
import PriceInfo from '@/components/PriceInfo';
import global from '@/utils/global';
import { skuService } from '../service';

interface Props {
  packageSkuInfo: PackageSkuInfo;
  onSelected?: (scrollLeft: number) => void;
}

const SkuSelectorItem: React.FC<Props> = ({
  packageSkuInfo,
  onSelected,
}: Props) => {
  // const imageSize = 36;
  const { actPackage } = packageSkuInfo;
  const dispatch: Dispatch = useDispatch();
  const [selectedSkuId, setSelectedSkuId] = useState(0);
  const selectedProductSku: ProductSku | undefined = useSelector(
    ({ product }: ConnectState) => product.selectedProductSku,
  );
  const divRef = useRef<HTMLDivElement>(null);
  // const deviceInfo: DeviceInfoWithPackage = useSelector(
  //   ({ device }: ConnectState) => device.deviceInfo,
  // );

  // const price = useMemo(() => {
  //   // console.log('price', packageSkuInfo);
  //   if (packageSkuInfo.actPackage) {
  //     return packageSkuInfo.actPackage.actPrice;
  //   }
  //   if (
  //     packageSkuInfo.isRenew &&
  //     deviceInfo?.effectiveProductInfo?.skuId !== packageSkuInfo.id
  //   ) {
  //     return (packageSkuInfo.firstPrice || packageSkuInfo.price);
  //   }
  //   return packageSkuInfo.price;
  // }, [deviceInfo, packageSkuInfo]);

  useEffect(() => {
    if (!selectedProductSku) return;
    setSelectedSkuId(selectedProductSku.id);
  }, [selectedProductSku]);

  const onSelect = () => {
    if (selectedProductSku && selectedProductSku.id === packageSkuInfo.id)
      return;

    skuService.selectedProductSkuId = packageSkuInfo.id;
    dispatch({
      type: 'product/selectProductSkuById',
      payload: packageSkuInfo.id,
    });
  };

  useEffect(() => {
    if (
      !selectedProductSku ||
      !packageSkuInfo ||
      selectedProductSku.id !== packageSkuInfo.id ||
      !onSelected ||
      !divRef ||
      !divRef.current
    )
      return;
    onSelected(divRef.current?.offsetLeft);
  }, [selectedProductSku, packageSkuInfo, divRef]);

  return (
    <div
      className={`rounded-xl w-40 mr-1.5 flex-shrink-0 relative overflow-visible p-0.5 ${
        selectedSkuId === packageSkuInfo.id
          ? 'sku-item-selected-border-color border-3'
          : // : 'border-transparent border-7'
            'sku-item-bg-color border-3 border-transparent'
      }`}
      onClick={onSelect}
      key={packageSkuInfo.id}
      ref={divRef}
    >
      <div
        className={`absolute flex items-center justify-center overflow-hidden`}
        style={{
          width: 86,
          height: 26,
          color: '#fff',
          borderRadius: '13px 0 13px 0',
          top: -3,
          right: -3,
        }}
      >
        {actPackage?.cornerMarkIcon || packageSkuInfo.cornerMarkIcon ? (
          <Image
            className="absolute right-0 z-1"
            style={{
              height: '26px',
            }}
            src={actPackage?.cornerMarkIcon || packageSkuInfo.cornerMarkIcon}
          />
        ) : null}
      </div>
      <div
        className={classNames('h-full', 'rounded-xl', {
          'sku-item-selected-bg-color': selectedSkuId === packageSkuInfo.id,
        })}
      >
        <div
          className={`rounded-t-xl flex flex-col items-center bg-no-repeat pt-8`}
          style={{
            height: 200,
            borderBottom: '1px solid',
            borderBottomColor:
              selectedSkuId === packageSkuInfo.id ? '#FFEADD' : '#EDEDED',
            // backgroundImage: selectedSkuId === packageSkuInfo.id ? `url(${activeBgImage})` : '',
          }}
        >
          <div className="normal-color text-1.5xl font-bold">
            {packageSkuInfo.name}
          </div>
          <p
            className="default-color font-normal text-xs mb-2.5"
            style={{
              visibility: packageSkuInfo.actPackage ? 'hidden' : 'visible',
            }}
          >
            ({packageSkuInfo.isRenew ? '连续' : ''}
            {serviceDurationNameObj[packageSkuInfo.serviceTimeUnit]})
          </p>
          {/* ${!packageSkuInfo.linePrice ? 'mb-5' : ''} */}
          <p
            className={classNames(`primary-text-color font-bold text-4xl mb-1`)}
            style={{ fontSize: 28, lineHeight: '1em' }}
          >
            <span className="font-normal text-1.5xl mr-1">
              {packageSkuInfo.actPackage?.price.currency?.currencySymbol ||
                packageSkuInfo?.currency?.currencySymbol ||
                global.DEFAULT_CURRENCY_SYMBOL}
            </span>
            {/* {price} */}
            <span
              className={classNames('', {
                'line-through':
                  packageSkuInfo?.actPackage &&
                  packageSkuInfo?.actPackage.price &&
                  packageSkuInfo.actPackage?.price.amount !==
                    packageSkuInfo.price,
              })}
            >
              {packageSkuInfo.price ? packageSkuInfo.price : 0}
            </span>
            {packageSkuInfo.serviceTimeUnit === ServiceTimeUnitEnum.MONTH ? (
              ''
            ) : (
              <span className="text-sm">/月</span>
            )}
          </p>
          {packageSkuInfo.serviceTimeUnit === ServiceTimeUnitEnum.YEAR ? (
            <p className="mb-0.5 secondary-text-color text-xs">
              <span className="">total: ￥{packageSkuInfo.totalPrice}</span>
            </p>
          ) : null}
          {!packageSkuInfo.actPackage && packageSkuInfo.linePrice ? (
            <p
              className={`default-color line-through`}
              style={{ fontSize: 12, marginBottom: 2, fontWeight: 600 }}
            >
              <PriceInfo
                symbol={
                  packageSkuInfo?.currency?.currencySymbol ||
                  global.DEFAULT_CURRENCY_SYMBOL
                }
                price={packageSkuInfo.linePrice}
              />
            </p>
          ) : null}
        </div>
        <div
          className={`px-3 py-5 overflow-hidden mb-1 min-h-[118px] ${
            selectedSkuId === packageSkuInfo.id ? '' : 'sku-item-bg-color'
          }`}
        >
          {(packageSkuInfo.benefits || []).map((benefit, index) => (
            <SkuBenefitItem benefit={benefit} key={String(index)} />
          ))}
        </div>
        <div className="underline text-center mb-5 cursor-pointer">
          查看全部权益
        </div>
      </div>
    </div>
  );
};
export default SkuSelectorItem;
