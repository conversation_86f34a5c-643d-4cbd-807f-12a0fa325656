import { StatusEnum } from '@/models/common.interface';
import { Benefit } from '@/models/product/interface';
import { Image, Swiper } from 'antd-mobile';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import ProgressIndicator from './ProgressIndicator';

const BenefitHighLight: React.FC = () => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [progress, setProgress] = useState(0);
  const [isAutoPlaying, setIsAutoPlaying] = useState(true);
  const progressRef = useRef<number>(0);
  const animationRef = useRef<number>();
  const swiperRef = useRef<any>();

  const benefits: Benefit[] = [
    {
      id: 1,
      name: 'HD VLOG™ 自动捕捉',
      icon: '',
      description: '自动为您捕捉精彩瞬间，制作宠物日常趣味VLOG',
      image: 'https://file.petkit.cn/static/product/d4s-hero/vlog-demo-1.jpg',
      isCoreBenefit: StatusEnum.ENABLE,
      attributeText: '',
      attributeSelectedText: '',
      deviceType: 'D4sh',
      sort: 1,
      handpick: true,
    },
    {
      id: 2,
      name: 'HD VLOG™ 智能剪辑',
      icon: '',
      description: '智能识别宠物行为，自动生成有趣的视频内容',
      image: 'https://file.petkit.cn/static/product/d4s-hero/vlog-demo-2.jpg',
      isCoreBenefit: StatusEnum.ENABLE,
      attributeText: '',
      attributeSelectedText: '',
      deviceType: 'D4sh',
      sort: 2,
      handpick: true,
    },
    {
      id: 3,
      name: 'HD VLOG™ 精彩时刻',
      icon: '',
      description: '记录宠物的每一个可爱瞬间，让美好永远留存',
      image: 'https://file.petkit.cn/static/product/d4s-hero/vlog-demo-3.jpg',
      isCoreBenefit: StatusEnum.ENABLE,
      attributeText: '',
      attributeSelectedText: '',
      deviceType: 'D4sh',
      sort: 3,
      handpick: true,
    },
    {
      id: 4,
      name: 'HD VLOG™ 健康分析',
      icon: '',
      description: '通过视频分析宠物健康状况，提供专业建议',
      image: 'https://file.petkit.cn/static/product/d4s-hero/vlog-demo-4.jpg',
      isCoreBenefit: StatusEnum.ENABLE,
      attributeText: '',
      attributeSelectedText: '',
      deviceType: 'D4sh',
      sort: 4,
      handpick: true,
    },
    {
      id: 5,
      name: 'HD VLOG™ 云端存储',
      icon: '',
      description: '所有精彩内容自动保存至云端，随时随地回看',
      image: 'https://file.petkit.cn/static/product/d4s-hero/vlog-demo-5.jpg',
      isCoreBenefit: StatusEnum.ENABLE,
      attributeText: '',
      attributeSelectedText: '',
      deviceType: 'D4sh',
      sort: 5,
      handpick: true,
    },
    {
      id: 6,
      name: 'HD VLOG™ 分享功能',
      icon: '',
      description: '一键分享精彩内容到社交平台，与朋友共享快乐',
      image: 'https://file.petkit.cn/static/product/d4s-hero/vlog-demo-6.jpg',
      isCoreBenefit: StatusEnum.ENABLE,
      attributeText: '',
      attributeSelectedText: '',
      deviceType: 'D4sh',
      sort: 6,
      handpick: true,
    },
  ];

  const startProgressAnimation = useCallback(() => {
    const startTime = Date.now();
    const duration = 3000; // 3秒

    const animate = () => {
      if (!isAutoPlaying) return;

      const elapsed = Date.now() - startTime;
      const newProgress = Math.min((elapsed / duration) * 100, 100);

      progressRef.current = newProgress;
      setProgress(newProgress);

      if (newProgress < 100) {
        animationRef.current = requestAnimationFrame(animate);
      } else {
        // 进度完成，切换到下一张
        const nextIndex = (currentIndex + 1) % benefits.length;
        setCurrentIndex(nextIndex);
        if (swiperRef.current) {
          swiperRef.current.swipeTo(nextIndex);
        }
      }
    };

    animationRef.current = requestAnimationFrame(animate);
  }, [currentIndex, benefits.length, isAutoPlaying]);

  const resetProgress = useCallback(() => {
    if (animationRef.current) {
      cancelAnimationFrame(animationRef.current);
    }
    progressRef.current = 0;
    setProgress(0);
  }, []);

  const handleSlideChange = useCallback(
    (index: number) => {
      setCurrentIndex(index);
      resetProgress();
    },
    [resetProgress],
  );

  const handleTouchStart = useCallback(() => {
    setIsAutoPlaying(false);
    resetProgress();
  }, [resetProgress]);

  const handleTouchEnd = useCallback(() => {
    setTimeout(() => {
      setIsAutoPlaying(true);
    }, 100);
  }, []);

  useEffect(() => {
    if (isAutoPlaying) {
      const timer = setTimeout(() => {
        startProgressAnimation();
      }, 100);
      return () => clearTimeout(timer);
    } else {
      resetProgress();
    }
  }, [currentIndex, isAutoPlaying, startProgressAnimation, resetProgress]);

  useEffect(() => {
    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, []);

  return (
    <div className="mb-9">
      {/* 标题区域 */}
      <h2 className="text-3xl font-bold mb-5 active-color text-center">
        智见·守护·在线
        <br />
        全天候AI守护者
      </h2>

      {/* Swiper 轮播区域 */}
      <Swiper
        ref={swiperRef}
        indicator={(total, current) => (
          <div className="flex justify-center items-center mt-6 space-x-3">
            {Array.from({ length: total }).map((_, index) => (
              <ProgressIndicator
                key={index}
                isActive={index === current}
                progress={index === current ? progress : 0}
              />
            ))}
          </div>
        )}
        className="w-full"
        allowTouchMove
        stuckAtBoundary={false}
        onIndexChange={handleSlideChange}
      >
        {benefits.map((benefit) => (
          <Swiper.Item key={benefit.id}>
            <div className="text-center w-full">
              {/* 图片展示区域 */}
              <div className="flex justify-center mb-5 w-full">
                <Image
                  src={benefit.image}
                  alt={benefit.name}
                  className="w-full"
                  style={{
                    objectFit: 'contain',
                  }}
                  placeholder="加载中..."
                />
              </div>

              {/* 描述区域 */}
              <div className="px-4">
                <div className="mb-2 text-2xl font-bold text-secondary-color">
                  {benefit.name}
                </div>
                <p className="secondary-text-color text-sm leading-relaxed">
                  {benefit.description}
                </p>
              </div>
            </div>
          </Swiper.Item>
        ))}
      </Swiper>
    </div>
  );
};

export default BenefitHighLight;
