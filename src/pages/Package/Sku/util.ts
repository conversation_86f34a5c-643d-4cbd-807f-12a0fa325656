import { ProductSku, ServiceTimeUnitEnum } from '@/models/product/interface';
import { productService } from '@/models/product/service';
import { uuid } from '@/utils/uuid';
import { OrderDuration, PackageSkuInfo } from './interface';

// const serviceTimeUnitDays: { [key in ServiceDurationEnumType]: number } = {
//   [ServiceDurationEnum.YEAR]: 365,
//   [ServiceDurationEnum.MONTH]: 365 / 12,
//   [ServiceDurationEnum.DAY]: 1,
//   [ServiceDurationEnum.SEASON]: 365 / 3,
// };

export const serviceTimeUnitNames: {
  [key in ServiceTimeUnitEnum]: string;
} = {
  [ServiceTimeUnitEnum.YEAR]: '年',
  [ServiceTimeUnitEnum.MONTH]: '月',
  [ServiceTimeUnitEnum.DAY]: '日',
};

export const serviceDurationNameObj: {
  [key in ServiceTimeUnitEnum]: string;
} = {
  [ServiceTimeUnitEnum.YEAR]: '包年',
  [ServiceTimeUnitEnum.MONTH]: '包月',
  [ServiceTimeUnitEnum.DAY]: '包天',
};

// const oneDayLoopBenefit: Benefit = {
//   id: 1,
//   name: '完整视频1天循环',
//   icon: oneDayLoop,
//   description: '完整视频1天循环',
//   image: '',
// };
// const sevenDaysLoopBenefit: Benefit = {
//   id: 2,
//   name: '完整视频7天循环',
//   icon: sevenDaysLoop,
//   description: '完整视频7天循环',
//   image: '',
// };
// const benefits: Benefit[] = [
//   {
//     id: 3,
//     name: '动态视频7天循环',
//     icon: sevenDaysLoop,
//     description: '动态视频7天循环',
//     image: '',
//   },
//   {
//     id: 4,
//     name: '云端高清录像',
//     icon: fullDisplay,
//     description: '云端高清录像',
//     image: '',
//   },
// ];

const transferProductSkuToPackageSkuInfo = (
  productSku: ProductSku,
  serviceTimeUnit: ServiceTimeUnitEnum,
): PackageSkuInfo => {
  const skuInfo: PackageSkuInfo = {
    id: productSku.id,
    name: productSku.shortName,
    price: productSku.price.price,
    totalPrice: productSku.price.price,
    linePrice: productSku.price.linePrice,
    // pricePerDay: +(
    //   productSku.price.price / serviceTimeUnitDays[productSku.serviceTimeUnit]
    // ),
    pricePerUnit: {
      serviceTimeUnit: '天',
      symbol: productSku.price.currency?.currencySymbol || '$',
      price: 0,
    },
    iconUrl: productSku.cornerMarkIcon,
    capacities: productSku.capacities,
    cycleTime: productSku.cycleTime,
    isRenew: productSku.price.isReNew,
    firstPrice: productSku.price.firstPhasePrice || 0,
    relationSkuId: productSku.relationSkuId,
    description: productSku.description,
    cornerMarkIcon: productSku.cornerMarkIcon,
    serviceTimeUnit,
    // benefits: [
    // ],
    benefits: productSku.benefits.filter((benefit) => benefit.handpick),
    actPackage: productSku.actPackage,
    currency: productSku.price.currency,
  };

  const MONTHS_PER_YEAR = 12;
  const DAYS_PER_MONTH = 30;
  const skuServiceTimeUnit =
    productSku.actPackage?.serviceTimeUnit || productSku.serviceTimeUnit;
  const servicePrice =
    productSku.actPackage?.price.amount || productSku.price.price;

  switch (skuServiceTimeUnit) {
    case ServiceTimeUnitEnum.YEAR:
      skuInfo.pricePerUnit.serviceTimeUnit =
        serviceTimeUnitNames[ServiceTimeUnitEnum.MONTH];
      skuInfo.pricePerUnit.price = +(servicePrice / MONTHS_PER_YEAR).toFixed(2);
      skuInfo.price = +(servicePrice / MONTHS_PER_YEAR).toFixed(2);
      skuInfo.totalPrice = servicePrice;
      break;
    case ServiceTimeUnitEnum.MONTH:
      skuInfo.pricePerUnit.serviceTimeUnit = '天';
      skuInfo.pricePerUnit.price = +(servicePrice / DAYS_PER_MONTH).toFixed(2);
      skuInfo.totalPrice = servicePrice;
      break;
    default:
      skuInfo.pricePerUnit.serviceTimeUnit = '天';
      skuInfo.pricePerUnit.price = +servicePrice.toFixed(2);
      skuInfo.totalPrice = servicePrice;
      break;
  }

  return skuInfo;
};

export const transferProductSkuListToOrderDurationList = (
  packageSkuList: ProductSku[],
): OrderDuration[] => {
  // 定义一个订单持续时间列表
  const orderDurationList: OrderDuration[] = [];
  // 定义一个服务时间产品sku对象
  const serviceDurationProductSkuObj: {
    [ServiceTimeUnitEnum.YEAR]: ProductSku[];
    [ServiceTimeUnitEnum.MONTH]: ProductSku[];
  } = {
    [ServiceTimeUnitEnum.MONTH]: [],
    [ServiceTimeUnitEnum.YEAR]: [],
    // [ServiceDurationEnum.SEASON]: [],
    // [ServiceDurationEnum.DAY]: [],
  };

  // serviceDurationProductSkuObj[ServiceDurationEnum.MONTH] = [...packageSkuList];

  for (let i = 0; i < packageSkuList.length; i++) {
    const sku = packageSkuList[i];
    if (!sku) continue;

    // if (sku.chargeType === ChargeTypeEnum.FREE) {
    //   serviceDurationProductSkuObj[ServiceDurationEnum.MONTH].push(sku);
    //   continue;
    // }

    const serviceDuration = productService.getSkuServiceTimeUnit(
      sku.serviceTime,
      sku.serviceTimeUnit,
    );

    if (
      !serviceDuration ||
      !serviceDurationProductSkuObj ||
      serviceDuration === ServiceTimeUnitEnum.DAY ||
      !serviceDurationProductSkuObj[serviceDuration]
    ) {
      continue;
    }

    serviceDurationProductSkuObj[serviceDuration].push(sku);
  }

  for (const key in serviceDurationProductSkuObj) {
    if (serviceDurationProductSkuObj[key as 'YEAR' | 'MONTH']) {
      const _packageSkuList: ProductSku[] =
        serviceDurationProductSkuObj[key as 'YEAR' | 'MONTH'] || [];
      // if (!_packageSkuList ||!_packageSkuList.length) continue;

      // console.log('transferProductSkuListToOrderDurationList', _packageSkuList);
      const orderDuration: OrderDuration = {
        key: uuid(),
        type: key as ServiceTimeUnitEnum,
        name: serviceDurationNameObj[key as ServiceTimeUnitEnum],
        packageSkuList: _packageSkuList.map((sku) =>
          transferProductSkuToPackageSkuInfo(sku, key as ServiceTimeUnitEnum),
        ),
      };
      orderDurationList.push(orderDuration);
    }
  }

  // 将orderDurationList中的packageSkuList为空数组的orderDuration过滤掉
  const _orderDurationList = orderDurationList.filter(
    (orderDuration) => orderDuration.packageSkuList.length,
  );
  return _orderDurationList;
};
