import handpickSvg from '@/assets/nike.svg';
import { Benefit } from '@/models/product/interface';
import classNames from 'classnames';
import React from 'react';

interface Props {
  benefit: Benefit;
}

const SkuBenefitItem: React.FC<Props> = ({ benefit }: Props) => {
  return (
    <div
      className={classNames('w-full flex items-center mb-3 whitespace-nowrap')}
    >
      <img className={classNames('w-[14px] h-[14px] mr-2')} src={handpickSvg} />
      <div className="flex items-center normal-color">
        <span className="text-base inline-block mr-1 font-bold">
          {benefit.attributeSelectedText}天
        </span>{' '}
        <span className="text-base inline-block">{benefit.name}</span>
      </div>
    </div>
  );
};

export default SkuBenefitItem;
