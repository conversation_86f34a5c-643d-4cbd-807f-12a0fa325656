import pic1 from '@/assets/entrance/en/pic1.gif';
import pic2 from '@/assets/entrance/en/pic2.gif';
import pic3 from '@/assets/entrance/en/pic3.gif';
import pic4 from '@/assets/entrance/en/pic4.gif';

export default {
  // 全局
  'book.name': '{bookName}',
  noData: 'No data',
  and: 'and',
  openAppTitle: 'Open App',
  openAppContent: 'The PETKIT APP is to be opened...',
  openAppConfirm: 'Open',
  openAppCancel: 'Cancel',
  cancel: 'Cancel',
  'repeat.order.mention': 'Please finish your pending order first',
  'show.order.list': 'Check orders',
  monthly: 'Monthly',
  yearly: 'Yearly',
  daily: 'Daily',
  seasonly: 'Seasonly',
  'cloud.service.description': 'PETKIT Care+ Description',
  'cloud.service.protocol': 'PETKIT Care+ Description Agreement',
  'renew.service.protocol': 'Auto-renewal Service Agreement',
  year: 'year',
  month: 'month',
  day: 'day',
  season: 'quarter',
  nextYear: 'next year',
  nextMonth: 'next month',
  nextDay: 'tomorrow',
  nextSeason: 'next quarter',
  continuous: 'Auto-renewal',
  continuousYear: 'Annual Auto-renewal',
  continuousMonth: 'Monthly Auto-renewal',
  continuousDay: 'Daily Auto-renewal',
  tipTitle: 'Tips',
  ok: 'Ok',
  known: 'Got it',
  copy: 'Copy',
  copySuccessfully: 'Copy completed',
  copyFailly: 'Copy failed',
  'payConfirm.content': 'Have you completed the payment?',
  'payConfirm.done': 'Done',
  'payConfirm.not': 'No',
  'subConfirm.content': 'Have you finished the payment contract?',
  'subConfirm.done': 'Done',
  'subConfirm.not': 'No',
  'unknownDevice.tip':
    'This device is not available. Please check if it is bound.',
  receiving: 'Applying...',
  'global.receive.error':
    'Free trail not available, please contact customer service',
  'global.receive.result.fail': 'Applying failed',
  'global.redirect.loading': 'Loading...',
  'global.purhcase.title.button.bottom.1': 'Read ',
  'global.purhcase.title.button.bottom.2': ' before subscribing ',
  // selector页面
  'package.selector.instruction.title': 'PETKIT Care+ Description',
  'package.selector.title': 'PETKIT Care+',
  'package.selector.device.subtitle.default': 'PETKIT Care+ is not activated.',
  'package.selector.device.service.status.waiting': 'Pending',
  'package.selector.device.service.status.waiting.lowerCase': 'pending',
  'package.selector.device.service.expiration.title': 'Valid until: ',
  'package.selector.device.pending.service.expiration.title': 'Valid until ',
  // 'package.selector.buttonText': 'Pay now',
  'package.selector.subDisabledTip': 'Payment contract terminated',
  'package.selector.subCanceledTip': 'Payment contract canceled',
  'package.selector.subErrorTip':
    'Please reinitiate payment contract confirmation again',
  'package.selector.purchase.fail1.1': 'You already have a ',
  'package.selector.purchase.fail1.2':
    ' plan, cannot purchase this plan at the moment.',
  'package.selector.purchase.fail2.1': ' ',
  'package.selector.purchase.fail2.2':
    ' plan being in service right now, please cancel subscription before changing plan.',
  'package.selector.purchase.fail3.1': ' ',
  'package.selector.purchase.fail3.2':
    ' plan already being in service right now with the device, ',
  'package.selector.purchase.fail3.3': ' valid until ',
  'package.selector.purchase.fail3.4': ', cannot be purchased again.',
  // sku权益信息
  'package.selector.benefit.title': 'Benefits',
  'package.selector.benefit.more': 'More',
  'package.selector.benefit.table.free': 'Free',
  'package.selector.benefit.table.price': 'Price',
  'package.selector.benefit.table.image': 'Image',
  'package.selector.benefit.table.highlight': 'Highlight',
  'package.selector.benefit.table.1080P': '1080P Playback',
  'package.selector.benefit.table.vlog': 'VLOG',
  'package.selector.benefit.table.displayTheme': 'Display Theme',
  'package.selector.benefit.table.one.day.loop': '1-day Loop',
  'package.selector.benefit.table.seven.day.loop': '7-day Loop',
  'package.selector.benefit.table.thirty.day.loop': '30-day Loop',
  'package.selector.benefit.table.support': 'Support',
  'package.selector.benefit.table.fecesPic': 'Pet Feces Chart',
  'package.selector.benefit.table.fullPlayback': 'Full Playback',
  'package.selector.benefit.table.fullPlayback.free': '30s SD video Today only',
  'package.selector.benefit.table.fullPlayback.basic':
    'full time HD video 1-day Loop',
  'package.selector.benefit.table.fullPlayback.premium':
    'full time HD video 7-day Loop',
  'package.selector.benefit.table.fullPlayback.premiumPlus':
    'full time HD video 30-day Loop',
  'package.selector.benefit.table.healthyAnalysis': 'Health Analysis',
  // 开通按钮
  'package.selector.buttonText.activity':
    '{number}-{unit} Trial at {priceSymbol}{price}',
  'package.selector.buttonText.activity.free': '{number}-{unit} Free Trial',
  // 右上角按钮
  'package.selector.exchange.button': 'Redemption Center',
  'package.selector.exchange.content': 'Contact the retailer for more details',
  'package.selector.exchange.error.noDevice': 'No available device yet!',
  'package.selector.myOrder.button': 'Order Management',

  'package.selector.activity.buttonText': '{number}-{unit} {name} ',
  'package.selector.activity.buttonText.free': 'for free',
  // 'package.selector.activity.buttonText.notFree': 'at ${price}}',

  'package.selector.activity.duration': 'On Special: ',
  'package.selector.normal.buttonText': 'Buy Now',
  'package.selector.normal.buttonTip':
    'It will be renewed automatically. Cancel any time.',
  // confirm页面
  'package.selector.confirm.title': 'Order settlement',
  'package.selector.confirm.payPrice': 'Payment Amount',
  'package.selector.confirm.renewDes':
    'It will renew {nextTime} automatically. Cancel any time.',
  'package.selector.confirm.restPayTip': 'Remaining Time',
  'package.selector.confirm.paying': 'Pay now',
  // confirm页面中的packageInfo
  'package.selector.confirm.packageInfo.name.title': 'Plan Name: ',
  'package.selector.confirm.packageInfo.expiration.title': 'Validity Date',
  'package.selector.confirm.packageInfo.orderType.title': 'Order Type: ',
  'package.selector.confirm.packageInfo.orderType.text': '{number} {unit}',
  // order页面
  'order.title': 'Order',
  'order.endTip': 'End',
  'order.item.payPrice': 'Total',
  'order.item.cancel': 'Cancel',
  'order.item.cancelSuccess​': 'Order Cancelled',
  'order.noData': 'No order',
  'order.status.unkown': ' ',
  'order.status.created': 'Order Created',
  'order.status.waitDeduct': 'Pending to pay',
  'order.status.paying': 'To be paid',
  'order.status.paySucceed': 'Payment successful',
  'order.status.payFailed': 'Payment failed',
  'order.status.refundFailed': 'Refund failed',
  'order.status.cancelled': 'Cancelled',
  'order.status.cancelledSystem': 'Cancelled',
  'order.status.finished': 'Completed',
  'order.info.no.title': 'Order Number: ',
  'order.info.payTime.title': 'Order Time: ',
  'order.info.payType.title': 'Payment Method: ',
  'order.info.isRenew.title': 'Auto-renewal',
  'order.pay.type.free': 'Special',
  'order.pay.type.exchangeCoupon': 'Coupon',

  // 结算页面
  'result.title': 'Order Settlement',
  'result.loading': 'Transaction being in process...',
  'result.success.title': 'Payment Successful',
  'result.success.button': 'My PETKIT Care+ Services',
  'result.fail.title': 'Payment failed',
  'result.fail.reason.title': 'Failure casued by: ',
  'result.fail.reason.content': 'Please check network condition',
  'result.fail.button.customerService': 'Contact customer service',
  'result.fail.button.repay': 'Continue to pay',
  'result.success.title.subscription': 'Signing Successful',
  'result.success.button.subscription': 'My PETKIT Care+ Services',
  'result.process.title.subscription': 'Please complete signing process first',
  'result.fail.title.subscription': 'Signing Failed',

  // payComp组件内容
  'payComp.order.cancel': 'Payment Cancelled',
  'payComp.order.error': 'Error ocurred, please pay it again',
  'payComp.pay.error.package': 'Select a plan first',
  'payComp.pay.error.valid.device.notExist': 'No valid device',
  'payComp.pay.error.device.invalid': 'Invalid device',

  // 领取成功页面
  'receive.success.title': 'Successfully',
  'receive.success.button.serviceDetail': 'Service Detail',
  'receive.success.button.start': 'Start',

  // 升级购买确认页
  'mention.title': 'Reminder',
  'mention.current.package.title': 'Current plan',
  'mention.current.package.info.paragraph.1': ' ',
  'mention.current.package.info.paragraph.2':
    ' plan being in service on the selected device (',
  'mention.current.package.info.paragraph.3': 'Validity Date: ',
  'mention.current.package.info.paragraph.4': ').',
  'mention.current.package.with.pending.info.paragraph.1':
    'The selected device being associated with ',
  'mention.current.package.with.pending.info.paragraph.2': 'multiple plans',
  'mention.current.package.with.pending.info.paragraph.3':
    ' (Make sure you want to activate ',
  'mention.current.package.with.pending.info.paragraph.4': '").',
  'mention.next.package.with.pending.info.paragraph.1':
    'The new plan will take effect after all the pending plans expire',
  'mention.next.package.with.pending.info.paragraph.2': ' ',
  'mention.next.package.with.pending.info.paragraph.3': '(',
  'mention.next.package.with.pending.info.paragraph.4': ')',
  'mention.next.package.with.pending.info.paragraph.5':
    ', and it is valid until ',
  'mention.next.package.with.pending.info.paragraph.6': '.',
  'mention.next.package.info.paragraph.1': ' ',
  'mention.next.package.info.paragraph.2':
    ' plan will take effect after current plan expires (',
  'mention.next.package.info.paragraph.3':
    '). The new plan will be valid until ',
  'mention.next.package.info.paragraph.4': '.',
  'mention.next.package.info.paragraph.4.sign':
    ' and be renewed after expiration automatically.',
  'mention.purhcase.title.button.loading': 'Purchasing...',
  'mention.purhcase.title.button': 'Buy at {currencySymbol}{purchaseAmount}',
  'mention.purhcase.title.button.free': 'For free',
  'mention.purhcase.title.button.sign':
    'It will be renewed automatically. Cancel any time.',
  'mention.activation.title': 'Please select payment method',
  'mention.activation.upgrade.card.title.1': 'Upgrade to ',
  'mention.activation.upgrade.card.title.1.free': 'Free to upgrade to',
  'mention.activation.upgrade.card.title.2': ' ',
  'mention.activation.upgrade.card.title.3': ' ',
  'mention.activation.upgrade.card.title.4': ' plan at {currencySymbol}{price}',
  'mention.activation.upgrade.card.title.4.free': ' ',
  'mention.activation.upgrade.card.subtitle': 'Cost Description',
  'mention.activation.upgrade.card.content':
    'Upgrading Cost = New Plan Price - Old Plan Single-Day Price * Remaining Days',
  'mention.activation.upgrade.card.paragraph.1':
    '*After plan upgrade, the new plan will take effect immediately and will be valid until {date}.',
  'mention.activation.upgrade.card.paragraph.2': ' ',
  'mention.activation.upgrade.card.paragraph.2.sign':
    ' It will renew at {periodPrice} automatically. Cancel any time.',
  'mention.activation.upgrade.card.button':
    'Upgrade Plan at {currencySymbol}{price}',
  'mention.activation.upgrade.card.button.free': 'Free to upgrade',
  'mention.activation.purchase.card.title.1': 'Purchase the ',
  'mention.activation.purchase.card.title.2': '{name}',
  'mention.activation.purchase.card.title.3': ' plan directly',
  'mentin.activation.purchase.card.expiration.title':
    '*After successful purchase, the new plan will take effect after the original plan expires ({effectiveTime}).',
  'mentin.activation.purchase.card.button.title': 'Buy now',
  'mention.activation.sign.card.title.1': 'Purchase the ',
  'mention.activation.sign.card.title.2': '{name}',
  'mention.activation.sign.card.title.3': ' plan directly',
  'mentin.activation.sign.card.expiration.title':
    '*After successful purchase, the new plan will take effect after the old plan expires ({start}). It is valid until {end}. It will be renewed automatically. Cancel any time.',
  'mentin.activation.sign.card.button.title': 'Buy now',

  // description页面
  // 云服务介绍内容
  'description.introduction.title': 'PETKIT Care+ 服务介绍',
  'description.introduction.paragraph1.title': 'Moments',
  'description.introduction.paragraph1.content':
    'Pet detection by AI. When the device detects pets strolling and eating, it automatically generates precious Moments for you to quickly catch sight of the daily life of your fur babies.',
  'description.introduction.paragraph1.image': pic1,
  'description.introduction.paragraph2.title': '1080P Full Clip',
  'description.introduction.paragraph2.content':
    'Pet videos are encrypted and stored in the cloud. No matter how frequent pet activities are, the device records all of them without cloud space limitation; even if the device is offline, you can view Full Clips stored in the cloud at any time.',
  'description.introduction.paragraph2.image': pic2,
  'description.introduction.paragraph3.title': '1080P Daily Story',
  'description.introduction.paragraph3.content':
    'The device automatically captures and selects highlights of your fur babies, and 1 Daily Story yesterday is produced for you to review; you can also share the video with your friends to enjoy your fur babies’s cuteness.',
  'description.introduction.paragraph3.image': pic3,
  'description.introduction.paragraph4.title': 'Video Speed Control',
  'description.introduction.paragraph4.content':
    'Up to 4x speedup is supported for your quick search.',
  'description.introduction.paragraph4.image': pic4,
  // 云服务说明协议
  'description.protocol.title': 'PETKIT Care+ Service Agreement',
  'description.protocol.url':
    '//file5.petkit.cn/matefw/2024/1/11/659f535398cd7c000d9909a9H0gVK2GWZ',
  // 自动续费服务协议
  'description.autoRenewProtocol.title':
    'PETKIT Care+ Auto-renewal Service Agreement',
  'description.autoRenewProtocol.url':
    '//file5.petkit.cn/hg-fw/2024/2/28/65dece49b1a800000cd3c3c9fTqK58r4i',
  // 每日精彩介绍页
  'dailyHighlights.instruction.title': 'Daily Story',
  'dailyHighlights.instruction.pagragraph1.title': 'Features',
  'dailyHighlights.instruction.pagragraph1.content':
    "The previous day's pet daily life are automatically edited into a short video for recording highlights. The short video will be generated after 8:00 every day.",
  'dailyHighlights.instruction.pagragraph2.title': 'Daily Story includes:',
  'dailyHighlights.instruction.pagragraph2.content1':
    'Capture every moment of pet',
  'dailyHighlights.instruction.pagragraph2.content2':
    "Show highlights of pet's day",
  'dailyHighlights.instruction.pagragraph2.content3':
    "Share pet's daily life with friends",

  // bs-1.16 - 本地化支付
  'order.pay.type.alipayPlus': 'Alipay+',
  'package.selector.sku.description': 'Only {symbol}{price} per {unit}',
  // Multiple Device Plan Module
  'multipleDevicePlan.card.title': 'Have multiple PETKIT devices?',
  'multipleDevicePlan.card.subtitle': 'Multi-device Plans',
  'multipleDevicePlan.card.description': 'One subscription, all devices on PETKIT Care+',
  'multipleDevicePlan.card.price': 'From',
  'multipleDevicePlan.page.title': 'Multiple Device Plan',
  'multipleDevicePlan.page.mainTitle': 'Have multiple PETKIT devices?',
  'multipleDevicePlan.page.mainSubtitle': 'Multi-device Plans',
  'multipleDevicePlan.page.mainDescription': 'One subscription, all devices on PETKIT Care+',
  'multipleDevicePlan.page.mainPrice': 'From',
  'multipleDevicePlan.page.features.title': 'Plan Benefits',
  'multipleDevicePlan.page.features.item1': 'Manage multiple devices with one subscription, convenient and efficient',
  'multipleDevicePlan.page.features.item2': 'Enjoy better prices and save more costs',
  'multipleDevicePlan.page.features.item3': 'All devices share advanced features and services',
  'multipleDevicePlan.page.pricing.title': 'Price Comparison',
  'multipleDevicePlan.page.pricing.single': 'Single Device Plan',
  'multipleDevicePlan.page.pricing.double': 'Dual Device Plan',
  'multipleDevicePlan.page.pricing.multi': 'Multi-device Plan (Buy Two Get One)',
  'multipleDevicePlan.page.pricing.month': 'Month',
  'multipleDevicePlan.page.button.select': 'Select Now',
  'multipleDevicePlan.page.button.learnMore': 'Learn More',
  'multipleDevicePlan.page.disclaimer': 'Actual prices are subject to the purchase page, promotional policies may have time limits',
};
